/* Custom Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeInUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

/* Product Filter Buttons */
.product-filter-btn.active {
    background-color: #4B0082;
    color: white;
}

.product-filter-btn {
    color: #6B7280;
}

/* Enhanced Glassmorphism effects */
.backdrop-blur-xl {
    backdrop-filter: blur(24px);
}

.backdrop-blur-md {
    backdrop-filter: blur(12px);
}

.backdrop-blur-sm {
    backdrop-filter: blur(4px);
}

/* Floating navbar styles */
#navbar {
    margin: 0 16px;
    border-radius: 16px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Floating label styles */
.peer:focus ~ label,
.peer:valid ~ label {
    transform: translateY(-12px) scale(0.75);
    color: #4B0082;
}

.peer:placeholder-shown ~ label {
    transform: translateY(0) scale(1);
    color: #6B7280;
}

/* Modal animations */
.modal-enter {
    animation: modalEnter 0.3s ease-out;
}

@keyframes modalEnter {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Loading skeleton */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Enhanced hover effects */
.group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
}

/* Button hover effects */
.btn-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Smooth transitions */
* {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #4B0082, #9370DB);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #9370DB, #4B0082);
}

/* Form focus states */
.form-input:focus {
    box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.1);
    border-color: #4B0082;
}

/* Dropdown menu animations */
.dropdown-enter {
    animation: dropdownEnter 0.2s ease-out;
}

@keyframes dropdownEnter {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile menu rounded corners */
#mobile-menu {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
}

/* Custom Select Dropdown Styling */
select:focus {
    box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.1);
}

/* Product Card Hover Effects */
.product-card:hover {
    transform: translateY(-8px);
}

/* Hero Section Enhancements */
.hero-slide {
    transition: opacity 0.5s ease-in-out;
}

/* Authentication Modal Enhancements */
.peer:focus ~ label {
    transform: translateY(-8px) scale(0.75);
    color: #4B0082;
}

.peer:valid ~ label {
    transform: translateY(-8px) scale(0.75);
    color: #4B0082;
}

.peer:placeholder-shown ~ label {
    transform: translateY(12px) scale(1);
    color: #6B7280;
}

/* Enhanced Button Styles */
button:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.1);
}

/* Notification Styles */
.notification {
    z-index: 9999;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading States */
.loaded {
    opacity: 1;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    #navbar {
        margin: 0 8px;
        border-radius: 12px;
    }
    
    .hero-slide h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }
}

/* Category Card Hover Effects */
.category-card:hover {
    transform: translateY(-4px) scale(1.02);
}

/* Testimonial Card Animations */
.testimonial-card {
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Enhanced Focus States */
input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #4B0082;
    box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.1);
}

/* Improved Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
